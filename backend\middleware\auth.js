const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { refreshZohoToken } = require('./tokenRefresh');

// Middleware to check if user is authenticated
const requireAuth = async (req, res, next) => {
    try {
        // Check for token in session first, then in headers
        let token = req.session.token;
        
        if (!token) {
            const authHeader = req.headers.authorization;
            if (authHeader && authHeader.startsWith('Bearer ')) {
                token = authHeader.substring(7);
            }
        }

        if (!token) {
            return res.redirect('/auth/login');
        }

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Get user from database
        const user = await User.findById(decoded.userId).select('-password');
        
        if (!user) {
            req.session.destroy();
            return res.redirect('/auth/login');
        }

        // Check if user is active
        if (user.status !== 'active') {
            req.session.destroy();
            return res.redirect('/auth/login?error=Account is not active');
        }

        // Update user activity
        await user.updateActivity(
            req.ip || req.connection.remoteAddress,
            req.get('User-Agent')
        );

        // Attach user to request
        req.user = user;

        // Check and refresh Zoho token if needed
        refreshZohoToken(req, res, next);
    } catch (error) {
        console.error('Auth middleware error:', error);
        req.session.destroy();
        return res.redirect('/auth/login?error=Invalid session');
    }
};

// Middleware to check if user is already authenticated (for login/register pages)
const requireGuest = (req, res, next) => {
    if (req.session.token) {
        return res.redirect('/');
    }
    next();
};

// Middleware to check if registration is enabled
const checkRegistrationEnabled = (req, res, next) => {
    if (process.env.REGISTER !== 'TRUE') {
        return res.status(404).render('errors/404', {
            title: 'Page Not Found',
            message: 'Registration is currently disabled.',
            layout: false
        });
    }
    next();
};

// Middleware to check user role
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.redirect('/auth/login');
        }

        if (!roles.includes(req.user.role)) {
            return res.status(403).render('errors/403', {
                title: 'Access Denied',
                message: 'You do not have permission to access this resource.',
                layout: false
            });
        }

        next();
    };
};

// Middleware to generate JWT token
const generateToken = (userId) => {
    return jwt.sign(
        { userId },
        process.env.JWT_SECRET,
        { expiresIn: '7d' }
    );
};

// Middleware to set user in locals for views
const setUserLocals = async (req, res, next) => {
    res.locals.user = null;
    res.locals.isAuthenticated = false;

    if (req.session.token) {
        try {
            const decoded = jwt.verify(req.session.token, process.env.JWT_SECRET);
            const user = await User.findById(decoded.userId).select('-password');
            
            if (user && user.status === 'active') {
                res.locals.user = user;
                res.locals.isAuthenticated = true;
            }
        } catch (error) {
            // Token is invalid, clear session
            req.session.destroy();
        }
    }

    next();
};

module.exports = {
    requireAuth,
    requireGuest,
    checkRegistrationEnabled,
    requireRole,
    generateToken,
    setUserLocals
};

const User = require('../models/User');
const { generateToken } = require('../middleware/auth');
const { getZohoConfig, getZohoConfigByEmail, isEmailAllowed, getAvailableAccounts, isValidAccountType } = require('../config/zohoConfig');
const { showSecretAuth, verifySecretAuth, clearSecretAuth } = require('../middleware/secretAuth');

// Show Zoho OAuth login page with account selection
const showLogin = (req, res) => {
    const error = req.query.error || null;
    const availableAccounts = getAvailableAccounts();

    res.render('auth/zoho-login', {
        title: 'Login - Cythro Mail Panel',
        error,
        accounts: availableAccounts,
        layout: false  // Disable layout for auth pages
    });
};

// Initiate Zoho OAuth flow for specific account
const initiateZohoAuth = (req, res) => {
    try {
        const { accountType } = req.params;

        if (!isValidAccountType(accountType)) {
            return res.redirect('/auth/login?error=Invalid account type');
        }

        const config = getZohoConfig(accountType);

        // Store account type in session for callback verification
        req.session.pendingAccountType = accountType;

        const zohoAuthUrl = `https://accounts.zoho.com/oauth/v2/auth?` +
            `scope=ZohoMail.messages.ALL,ZohoMail.accounts.READ,ZohoMail.folders.READ&` +
            `client_id=${config.clientId}&` +
            `response_type=code&` +
            `access_type=offline&` +
            `redirect_uri=${encodeURIComponent(config.redirectUri)}&` +
            `state=${accountType}`; // Add state parameter for extra security

        res.redirect(zohoAuthUrl);
    } catch (error) {
        console.error('OAuth initiation error:', error);
        res.redirect('/auth/login?error=Failed to initiate authentication');
    }
};

// Handle Zoho OAuth callback
const handleZohoCallback = async (req, res) => {
    try {
        const { code, error, state } = req.query;

        if (error) {
            return res.redirect('/auth/login?error=OAuth authorization failed');
        }

        if (!code) {
            return res.redirect('/auth/login?error=No authorization code received');
        }

        // Verify state parameter and get account type
        const accountType = state || req.session.pendingAccountType;
        if (!accountType || !isValidAccountType(accountType)) {
            return res.redirect('/auth/login?error=Invalid authentication state');
        }

        const config = getZohoConfig(accountType);

        // Exchange code for tokens using the appropriate config
        const tokenResponse = await fetch('https://accounts.zoho.com/oauth/v2/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                grant_type: 'authorization_code',
                client_id: config.clientId,
                client_secret: config.clientSecret,
                redirect_uri: config.redirectUri,
                code: code
            })
        });

        const tokenData = await tokenResponse.json();

        if (!tokenResponse.ok) {
            console.error('Token exchange error:', tokenData);
            return res.redirect('/auth/login?error=Failed to exchange authorization code');
        }

        // Get user info from Zoho
        const userResponse = await fetch('https://mail.zoho.com/api/accounts', {
            headers: {
                'Authorization': `Bearer ${tokenData.access_token}`
            }
        });

        const userData = await userResponse.json();

        if (!userResponse.ok) {
            console.error('User info error:', userData);
            return res.redirect('/auth/login?error=Failed to get user information');
        }

        // Get the Zoho account data
        const zohoAccount = userData.data[0]; // Get first account
        const userEmail = zohoAccount.primaryEmailAddress;

        // Verify that the email matches the expected account and is allowed
        if (!isEmailAllowed(userEmail)) {
            console.log(`Unauthorized login attempt from: ${userEmail}`);
            return res.redirect('/auth/login?error=Access denied. This email is not authorized.');
        }

        // Verify email matches the selected account type
        if (userEmail !== config.email) {
            console.log(`Email mismatch: expected ${config.email}, got ${userEmail}`);
            return res.redirect('/auth/login?error=Email does not match the selected account type.');
        }

        // Find or create user
        let user = await User.findOne({ email: userEmail });

        if (!user) {
            // Create new user with account type information
            user = new User({
                firstName: zohoAccount.displayName.split(' ')[0] || config.displayName.split(' ')[0],
                lastName: zohoAccount.displayName.split(' ').slice(1).join(' ') || config.displayName.split(' ').slice(1).join(' '),
                email: userEmail,
                displayName: zohoAccount.displayName || config.displayName,
                status: 'active',
                isEmailVerified: true,
                role: accountType === 'admin' ? 'admin' : 'user', // Set admin role for admin account
                apiKeys: {
                    zoho: {
                        accountId: zohoAccount.accountId,
                        accountType: accountType,
                        accessToken: tokenData.access_token,
                        refreshToken: tokenData.refresh_token,
                        tokenExpiry: new Date(Date.now() + tokenData.expires_in * 1000),
                        scope: tokenData.scope ? tokenData.scope.split(',') : []
                    }
                },
                emailAccounts: [{
                    provider: 'zoho',
                    email: userEmail,
                    displayName: zohoAccount.displayName || config.displayName,
                    isDefault: true,
                    isActive: true
                }]
            });
        } else {
            // Update existing user tokens and account type
            user.apiKeys.zoho.accountType = accountType;
            user.apiKeys.zoho.accessToken = tokenData.access_token;
            user.apiKeys.zoho.refreshToken = tokenData.refresh_token;
            user.apiKeys.zoho.tokenExpiry = new Date(Date.now() + tokenData.expires_in * 1000);
            user.apiKeys.zoho.scope = tokenData.scope ? tokenData.scope.split(',') : [];

            // Update role if admin
            if (accountType === 'admin') {
                user.role = 'admin';
            }
        }

        // Clear the pending account type from session
        delete req.session.pendingAccountType;

        await user.save();

        // Generate JWT token and set session
        const jwtToken = generateToken(user._id);
        req.session.token = jwtToken;
        req.session.userId = user._id;

        // Update user activity
        await user.updateActivity(
            req.ip || req.connection.remoteAddress,
            req.get('User-Agent')
        );

        // Redirect to dashboard
        res.redirect('/');

    } catch (error) {
        console.error('Zoho OAuth callback error:', error);
        res.redirect('/auth/login?error=Authentication failed');
    }
};

// Handle logout
const handleLogout = (req, res) => {
    // Clear secret auth session first
    clearSecretAuth(req, res, () => {
        req.session.destroy((err) => {
            if (err) {
                console.error('Logout error:', err);
            }
            res.redirect('/auth/secret');
        });
    });
};



module.exports = {
    showSecretAuth,
    verifySecretAuth,
    showLogin,
    initiateZohoAuth,
    handleZohoCallback,
    handleLogout
};


// Helper function to get the correct redirect URI based on environment
const getRedirectUri = () => {
    if (process.env.NODE_ENV === 'production') {
        // Use the production domain - update this to match your Vercel deployment URL
        return process.env.PRODUCTION_REDIRECT_URI;
    }
    return 'http://localhost:3000/auth/mail/callback';
};

// Zoho OAuth configurations for different accounts
const zohoConfigs = {
    support: {
        email: '<EMAIL>',
        clientId: process.env.ZOHO_SUPPORT_CLIENT_ID || '1000.******************************',
        clientSecret: process.env.ZOHO_SUPPORT_CLIENT_SECRET || '2e9404d2a62863d106906c9f3337693e95d6f3cac5',
        redirectUri: getRedirectUri(),
        displayName: 'Support Account',
        description: 'Customer support and help desk',
        icon: 'bi-headset'
    },
    contact: {
        email: '<EMAIL>',
        clientId: process.env.ZOHO_CONTACT_CLIENT_ID || '1000.FDOEED5J0BHTQK6WEG6QUQ2CQ5R44T',
        clientSecret: process.env.ZOHO_CONTACT_CLIENT_SECRET || '886dad8d164f4933d4bbca1cab94fba1a586f50c53',
        redirectUri: getRedirectUri(),
        displayName: 'Contact Account',
        description: 'General inquiries and business contact',
        icon: 'bi-envelope-at'
    },
    admin: {
        email: '<EMAIL>',
        clientId: process.env.ZOHO_ADMIN_CLIENT_ID || '1000.0V7X6HK5C1YIB658AOJWVR86WFIESP',
        clientSecret: process.env.ZOHO_ADMIN_CLIENT_SECRET || '4aa0797e607f34b7ce2e27ded9487dfad39cf27e82',
        redirectUri: getRedirectUri(),
        displayName: 'Admin Account',
        description: 'Administrative access and management',
        icon: 'bi-shield-lock'
    }
};

// Get configuration by account type
const getZohoConfig = (accountType) => {
    const config = zohoConfigs[accountType];
    if (!config) {
        throw new Error(`Invalid account type: ${accountType}`);
    }
    return config;
};

// Get configuration by email address
const getZohoConfigByEmail = (email) => {
    const accountType = Object.keys(zohoConfigs).find(
        key => zohoConfigs[key].email === email
    );
    
    if (!accountType) {
        throw new Error(`No configuration found for email: ${email}`);
    }
    
    return { ...zohoConfigs[accountType], accountType };
};

// Check if email is allowed
const isEmailAllowed = (email) => {
    return Object.values(zohoConfigs).some(config => config.email === email);
};

// Get all available account types for login selection
const getAvailableAccounts = () => {
    return Object.keys(zohoConfigs).map(key => ({
        type: key,
        ...zohoConfigs[key]
    }));
};

// Validate account type
const isValidAccountType = (accountType) => {
    return zohoConfigs.hasOwnProperty(accountType);
};

module.exports = {
    getZohoConfig,
    getZohoConfigByEmail,
    isEmailAllowed,
    getAvailableAccounts,
    isValidAccountType,
    zohoConfigs
};

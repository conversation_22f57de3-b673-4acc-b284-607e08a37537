const { body } = require('express-validator');

// Validation rules for sending email
const validateSendEmail = [
    body('to')
        .notEmpty()
        .withMessage('To field is required')
        .isEmail()
        .withMessage('Please enter a valid email address')
        .normalizeEmail(),
    
    body('cc')
        .optional()
        .custom((value) => {
            if (value) {
                const emails = value.split(',').map(email => email.trim());
                for (const email of emails) {
                    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error('Please enter valid CC email addresses');
                    }
                }
            }
            return true;
        }),
    
    body('bcc')
        .optional()
        .custom((value) => {
            if (value) {
                const emails = value.split(',').map(email => email.trim());
                for (const email of emails) {
                    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error('Please enter valid BCC email addresses');
                    }
                }
            }
            return true;
        }),
    
    body('subject')
        .optional()
        .isLength({ max: 255 })
        .withMessage('Subject must be less than 255 characters')
        .trim(),
    
    body('body')
        .optional()
        .isLength({ max: 50000 })
        .withMessage('Email body is too long (max 50,000 characters)'),
    
    body('priority')
        .optional()
        .isIn(['low', 'normal', 'high'])
        .withMessage('Invalid priority level')
];

// Validation rules for saving draft
const validateSaveDraft = [
    body('to')
        .optional()
        .custom((value) => {
            if (value) {
                const emails = value.split(',').map(email => email.trim());
                for (const email of emails) {
                    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error('Please enter valid email addresses');
                    }
                }
            }
            return true;
        }),
    
    body('cc')
        .optional()
        .custom((value) => {
            if (value) {
                const emails = value.split(',').map(email => email.trim());
                for (const email of emails) {
                    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error('Please enter valid CC email addresses');
                    }
                }
            }
            return true;
        }),
    
    body('bcc')
        .optional()
        .custom((value) => {
            if (value) {
                const emails = value.split(',').map(email => email.trim());
                for (const email of emails) {
                    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error('Please enter valid BCC email addresses');
                    }
                }
            }
            return true;
        }),
    
    body('subject')
        .optional()
        .isLength({ max: 255 })
        .withMessage('Subject must be less than 255 characters')
        .trim(),
    
    body('body')
        .optional()
        .isLength({ max: 50000 })
        .withMessage('Email body is too long (max 50,000 characters)')
];

module.exports = {
    validateSendEmail,
    validateSaveDraft
};

const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    // Basic Authentication Info
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    // Password removed - using OAuth authentication
    
    // Personal Information
    firstName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    lastName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    displayName: {
        type: String,
        trim: true,
        maxlength: 100
    },
    avatar: {
        type: String,
        default: null
    },
    
    // Mail Panel Configuration
    mailSettings: {
        signature: {
            type: String,
            default: '',
            maxlength: 1000
        },
        autoReply: {
            enabled: { type: Boolean, default: false },
            message: { type: String, default: '', maxlength: 1000 },
            startDate: Date,
            endDate: Date
        },
        emailsPerPage: {
            type: Number,
            default: 25,
            min: 10,
            max: 100
        },
        theme: {
            type: String,
            enum: ['dark', 'light', 'auto'],
            default: 'dark'
        },
        language: {
            type: String,
            default: 'en',
            maxlength: 5
        },
        timezone: {
            type: String,
            default: 'UTC'
        }
    },
    
    // Email Service Integration
    emailAccounts: [{
        provider: {
            type: String,
            enum: ['zoho', 'gmail', 'outlook', 'custom'],
            required: true
        },
        email: {
            type: String,
            required: true
        },
        displayName: String,
        isDefault: {
            type: Boolean,
            default: false
        },
        settings: {
            imapHost: String,
            imapPort: Number,
            smtpHost: String,
            smtpPort: Number,
            useSSL: { type: Boolean, default: true },
            accessToken: String,
            refreshToken: String,
            tokenExpiry: Date
        },
        isActive: {
            type: Boolean,
            default: true
        },
        addedAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    // API Keys and Tokens
    apiKeys: {
        zoho: {
            accountId: String,
            accountType: {
                type: String,
                enum: ['support', 'contact', 'admin'],
                required: true
            },
            clientId: String,
            clientSecret: String,
            accessToken: String,
            refreshToken: String,
            tokenExpiry: Date,
            scope: [String]
        }
    },
    
    // User Preferences
    preferences: {
        notifications: {
            email: { type: Boolean, default: true },
            desktop: { type: Boolean, default: false },
            sound: { type: Boolean, default: false }
        },
        privacy: {
            showOnlineStatus: { type: Boolean, default: true },
            allowReadReceipts: { type: Boolean, default: true }
        },
        security: {
            twoFactorEnabled: { type: Boolean, default: false },
            twoFactorSecret: String,
            backupCodes: [String],
            lastPasswordChange: { type: Date, default: Date.now }
        }
    },
    
    // Activity Tracking
    activity: {
        lastLogin: Date,
        lastActive: Date,
        loginCount: { type: Number, default: 0 },
        ipAddress: String,
        userAgent: String
    },
    
    // Email Statistics
    stats: {
        totalEmailsSent: { type: Number, default: 0 },
        totalEmailsReceived: { type: Number, default: 0 },
        totalEmailsRead: { type: Number, default: 0 },
        totalAttachments: { type: Number, default: 0 },
        storageUsed: { type: Number, default: 0 }, // in bytes
        lastStatsUpdate: { type: Date, default: Date.now }
    },
    
    // Account Status
    status: {
        type: String,
        enum: ['active', 'inactive', 'suspended', 'pending'],
        default: 'active'
    },
    isEmailVerified: {
        type: Boolean,
        default: false
    },
    emailVerificationToken: String,
    emailVerificationExpiry: Date,
    
    // Password Reset
    passwordResetToken: String,
    passwordResetExpiry: Date,
    
    // Account Management
    role: {
        type: String,
        enum: ['user', 'admin', 'moderator'],
        default: 'user'
    },
    subscription: {
        plan: {
            type: String,
            enum: ['free', 'basic', 'premium', 'enterprise'],
            default: 'free'
        },
        startDate: Date,
        endDate: Date,
        isActive: { type: Boolean, default: true }
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Virtual for full name
userSchema.virtual('fullName').get(function() {
    return `${this.firstName} ${this.lastName}`;
});

// Virtual for display name or full name
userSchema.virtual('name').get(function() {
    return this.displayName || this.fullName;
});

// OAuth authentication - no password hashing needed

// Method to update last activity
userSchema.methods.updateActivity = function(ipAddress, userAgent) {
    this.activity.lastActive = new Date();
    this.activity.ipAddress = ipAddress;
    this.activity.userAgent = userAgent;
    return this.save();
};

// Method to get default email account
userSchema.methods.getDefaultEmailAccount = function() {
    return this.emailAccounts.find(account => account.isDefault) || this.emailAccounts[0];
};

// Static method to find by email
userSchema.statics.findByEmail = function(email) {
    return this.findOne({ email: email.toLowerCase() });
};

// Indexes for performance
// Note: email index is already created by unique: true in schema definition
userSchema.index({ 'emailAccounts.email': 1 });
userSchema.index({ status: 1 });
userSchema.index({ createdAt: -1 });

module.exports = mongoose.model('User', userSchema);

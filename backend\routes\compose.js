const express = require('express');
const router = express.Router();
const { requireAuth } = require('../middleware/auth');
const { validateSendEmail, validateSaveDraft } = require('../middleware/composeValidation');
const {
    showCompose,
    sendEmail,
    saveDraft,
    getDrafts,
    deleteDraft
} = require('../controllers/composeController');

// Show compose page
router.get('/', requireAuth, showCompose);

// Send email (temporarily bypass validation for debugging)
router.post('/send', requireAuth, sendEmail);

// Save draft
router.post('/draft', requireAuth, validateSaveDraft, saveDraft);

// Get drafts
router.get('/drafts', requireAuth, getDrafts);

// Delete draft
router.delete('/draft/:draftId', requireAuth, deleteDraft);

module.exports = router;

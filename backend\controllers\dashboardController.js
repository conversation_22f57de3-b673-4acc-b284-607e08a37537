const User = require('../models/User');
const zohoMailService = require('../services/zohoMailService');
const DOMPurify = require('isomorphic-dompurify');

// Function to decode HTML entities
const decodeHtmlEntities = (text) => {
    if (!text) return text;
    return text
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&nbsp;/g, ' ');
};

// Show dashboard
const showDashboard = async (req, res) => {
    try {
        const user = req.user;

        // Get Zoho account ID from user's API keys
        const zohoAccountId = user.apiKeys.zoho.accountId;

        if (!zohoAccountId) {
            return res.status(400).render('errors/500', {
                title: 'Configuration Error',
                message: 'Zoho account not properly configured. Please re-authenticate.',
                layout: false
            });
        }

        // Fetch recent emails from inbox
        let recentEmails = [];
        let emailStats = {
            totalEmails: 0,
            unreadEmails: 0,
            sentEmails: 0,
            draftEmails: 0
        };

        try {
            // Get recent inbox emails (limit to 10 for dashboard)
            recentEmails = await zohoMailService.getFormattedInboxEmails(
                user._id,
                zohoAccountId,
                {
                    limit: 10,
                    status: 'all',
                    sortBy: 'date',
                    sortorder: false // newest first
                }
            );

            // Calculate email statistics
            emailStats.totalEmails = recentEmails.length;
            emailStats.unreadEmails = recentEmails.filter(email => !email.isRead).length;

            // Get user's stored stats for sent emails (from previous sessions)
            emailStats.sentEmails = user.stats.totalEmailsSent || 0;
            emailStats.draftEmails = 0; // Will be implemented later

        } catch (emailError) {
            console.error('Failed to fetch emails for dashboard:', emailError);
            // Continue with empty emails array if API fails
            recentEmails = [];
        }

        // Get user's default email account
        const defaultEmailAccount = user.getDefaultEmailAccount();

        res.render('dashboard', {
            title: 'Dashboard - Cythro Mail Panel',
            user,
            stats: emailStats,
            recentEmails,
            defaultEmailAccount,
            zohoAccountId
        });
    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).render('errors/500', {
            title: 'Server Error',
            message: 'An error occurred while loading the dashboard.',
            layout: false
        });
    }
};

// Show inbox
const showInbox = async (req, res) => {
    try {
        const user = req.user;
        const currentFolder = req.params.folder || 'inbox';
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 25;
        const status = req.query.status || 'all'; // all, read, unread

        // Get Zoho account ID
        const zohoAccountId = user.apiKeys.zoho.accountId;

        if (!zohoAccountId) {
            return res.status(400).render('errors/500', {
                title: 'Configuration Error',
                message: 'Zoho account not properly configured. Please re-authenticate.'
            });
        }

        let emails = [];
        let folderInfo = {
            name: currentFolder,
            displayName: currentFolder.charAt(0).toUpperCase() + currentFolder.slice(1),
            totalEmails: 0,
            unreadCount: 0
        };

        try {
            if (currentFolder === 'inbox') {
                // Fetch inbox emails
                emails = await zohoMailService.getFormattedInboxEmails(
                    user._id,
                    zohoAccountId,
                    {
                        start: (page - 1) * limit + 1,
                        limit: limit,
                        status: status,
                        sortBy: 'date',
                        sortorder: false, // newest first
                        includeto: true
                    }
                );

                // Calculate folder stats
                folderInfo.totalEmails = emails.length;
                folderInfo.unreadCount = emails.filter(email => !email.isRead).length;

            } else {
                // Handle other folders (sent, drafts, etc.)
                const folderMapping = {
                    'sent': 'Sent',
                    'drafts': 'Drafts',
                    'trash': 'Trash',
                    'spam': 'Spam'
                };

                const zohoFolderName = folderMapping[currentFolder];

                if (zohoFolderName) {
                    // Fetch emails from the specific folder
                    emails = await zohoMailService.getFormattedEmailsByFolder(
                        user._id,
                        zohoAccountId,
                        zohoFolderName,
                        {
                            start: (page - 1) * limit + 1,
                            limit: limit,
                            sortBy: 'date',
                            sortorder: false, // newest first
                            includeto: true
                        }
                    );

                    // Calculate folder stats
                    folderInfo.totalEmails = emails.length;
                    folderInfo.unreadCount = emails.filter(email => !email.isRead).length;
                } else {
                    // Unknown folder, show empty state
                    emails = [];
                }

                folderInfo.displayName = currentFolder.charAt(0).toUpperCase() + currentFolder.slice(1);
            }

        } catch (emailError) {
            console.error(`Failed to fetch ${currentFolder} emails:`, emailError);
            // Show error message but don't crash
            emails = [];
            req.flash = req.flash || (() => {});
            req.flash('error', 'Failed to load emails. Please try again.');
        }

        // Pagination info
        const pagination = {
            currentPage: page,
            totalPages: Math.ceil(folderInfo.totalEmails / limit),
            hasNext: emails.length === limit,
            hasPrev: page > 1,
            limit: limit
        };

        res.render('inbox', {
            title: `${folderInfo.displayName} - Cythro Mail Panel`,
            user,
            emails,
            currentFolder,
            folderInfo,
            pagination,
            status,
            zohoAccountId
        });
    } catch (error) {
        console.error('Inbox error:', error);
        res.status(500).render('errors/500', {
            title: 'Server Error',
            message: 'An error occurred while loading the inbox.',
            layout: false
        });
    }
};

// Show compose page
const showCompose = async (req, res) => {
    try {
        const user = req.user;
        const { replyTo, subject } = req.query;

        res.render('compose', {
            title: 'Compose Email - Cythro Mail Panel',
            user,
            replyTo: replyTo || '',
            subject: subject || ''
        });
    } catch (error) {
        console.error('Compose error:', error);
        res.status(500).render('errors/500', {
            title: 'Server Error',
            message: 'An error occurred while loading the compose page.',
            layout: false
        });
    }
};

// Show email view
const showEmailView = async (req, res) => {
    try {
        const user = req.user;
        const messageId = req.params.id;
        const folderId = req.query.folderId;

        // Get Zoho account ID
        const zohoAccountId = user.apiKeys.zoho.accountId;

        if (!zohoAccountId) {
            return res.status(400).render('errors/500', {
                title: 'Configuration Error',
                message: 'Zoho account not properly configured. Please re-authenticate.'
            });
        }

        if (!folderId) {
            return res.status(400).render('errors/500', {
                title: 'Invalid Request',
                message: 'Folder ID is required to view email.'
            });
        }

        try {
            // Fetch email content and details
            const [emailContent, emailDetails] = await Promise.all([
                zohoMailService.getEmailContent(user._id, zohoAccountId, folderId, messageId),
                zohoMailService.getEmailDetails(user._id, zohoAccountId, folderId, messageId)
            ]);

            // Determine if content is HTML
            const isHtmlContent = emailContent.mode === 'html' ||
                                 (emailContent.content && emailContent.content.includes('<')) ||
                                 (emailContent.textContent && emailContent.textContent.includes('<'));

            // Get the appropriate content
            let bodyContent = emailContent.content || emailContent.textContent || 'No content available';

            // Clean up HTML content if needed
            if (isHtmlContent && bodyContent) {
                // Sanitize HTML content for security
                bodyContent = DOMPurify.sanitize(bodyContent, {
                    ALLOWED_TAGS: ['p', 'div', 'span', 'br', 'strong', 'b', 'em', 'i', 'u', 'a', 'img', 'table', 'tr', 'td', 'th', 'tbody', 'thead', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li'],
                    ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'style', 'class', 'target', 'width', 'height', 'border', 'cellpadding', 'cellspacing'],
                    ALLOW_DATA_ATTR: false,
                    FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button'],
                    FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover']
                });
            }

            // Format email data for display
            const email = {
                id: messageId,
                folderId: folderId,
                subject: decodeHtmlEntities(emailDetails.subject) || '(No Subject)',
                from: {
                    email: decodeHtmlEntities(emailDetails.fromAddress),
                    name: decodeHtmlEntities(emailDetails.sender) || decodeHtmlEntities(emailDetails.fromAddress)
                },
                to: decodeHtmlEntities(emailDetails.toAddress),
                cc: emailDetails.ccAddress !== 'Not Provided' ? decodeHtmlEntities(emailDetails.ccAddress) : null,
                bcc: emailDetails.bccAddress !== 'Not Provided' ? decodeHtmlEntities(emailDetails.bccAddress) : null,
                date: new Date(parseInt(emailDetails.sentDateInGMT)),
                receivedTime: new Date(parseInt(emailDetails.receivedTime)),
                body: bodyContent,
                isHtml: isHtmlContent,
                priority: emailDetails.priority,
                hasAttachment: emailDetails.hasAttachment === '1',
                attachments: emailDetails.attachmentDetails || [],
                size: parseInt(emailDetails.size || 0),
                threadId: emailDetails.threadId,
                isRead: emailDetails.status === '1'
            };

            // Mark email as read if it's unread
            if (!email.isRead) {
                try {
                    await zohoMailService.markAsRead(user._id, zohoAccountId, messageId);
                    email.isRead = true;
                } catch (markReadError) {
                    console.error('Failed to mark email as read:', markReadError);
                    // Continue anyway, just log the error
                }
            }

            res.render('email-view', {
                title: `${email.subject} - Cythro Mail Panel`,
                user,
                email,
                zohoAccountId
            });

        } catch (emailError) {
            console.error('Failed to fetch email:', emailError);
            res.status(404).render('errors/404', {
                title: 'Email Not Found',
                message: 'The requested email could not be found or accessed.',
                layout: false
            });
        }

    } catch (error) {
        console.error('Email view error:', error);
        res.status(500).render('errors/500', {
            title: 'Server Error',
            message: 'An error occurred while loading the email.',
            layout: false
        });
    }
};

// API endpoint to get user dashboard data
const getDashboardData = async (req, res) => {
    try {
        const user = req.user;
        
        const dashboardData = {
            user: {
                id: user._id,
                name: user.name,
                email: user.email,
                avatar: user.avatar,
                role: user.role
            },
            stats: user.stats,
            emailAccounts: user.emailAccounts.map(account => ({
                id: account._id,
                provider: account.provider,
                email: account.email,
                displayName: account.displayName,
                isDefault: account.isDefault,
                isActive: account.isActive
            })),
            preferences: user.preferences,
            mailSettings: user.mailSettings
        };

        res.json({
            success: true,
            data: dashboardData
        });
    } catch (error) {
        console.error('Get dashboard data error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get dashboard data'
        });
    }
};

// API endpoint to mark email as read/unread
const markEmailStatus = async (req, res) => {
    try {
        const user = req.user;
        const { messageIds, action } = req.body; // action: 'read' or 'unread'
        const zohoAccountId = user.apiKeys.zoho.accountId;

        if (!zohoAccountId) {
            return res.status(400).json({
                success: false,
                message: 'Zoho account not configured'
            });
        }

        if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Message IDs are required'
            });
        }

        let result;
        if (action === 'read') {
            result = await zohoMailService.markAsRead(user._id, zohoAccountId, messageIds);
        } else if (action === 'unread') {
            result = await zohoMailService.markAsUnread(user._id, zohoAccountId, messageIds);
        } else {
            return res.status(400).json({
                success: false,
                message: 'Invalid action. Use "read" or "unread"'
            });
        }

        res.json({
            success: true,
            message: `Emails marked as ${action}`,
            data: result
        });

    } catch (error) {
        console.error('Mark email status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update email status'
        });
    }
};

// API endpoint to search emails
const searchEmails = async (req, res) => {
    try {
        const user = req.user;
        const { query, limit = 25, start = 1 } = req.query;
        const zohoAccountId = user.apiKeys.zoho.accountId;

        if (!zohoAccountId) {
            return res.status(400).json({
                success: false,
                message: 'Zoho account not configured'
            });
        }

        if (!query || query.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Search query is required'
            });
        }

        const emails = await zohoMailService.searchEmails(
            user._id,
            zohoAccountId,
            query.trim(),
            { limit: parseInt(limit), start: parseInt(start) }
        );

        const formattedEmails = emails.map(email =>
            zohoMailService.formatEmailForFrontend(email)
        );

        res.json({
            success: true,
            data: formattedEmails,
            query: query,
            total: formattedEmails.length
        });

    } catch (error) {
        console.error('Search emails error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to search emails'
        });
    }
};

// API endpoint to get folders
const getFolders = async (req, res) => {
    try {
        const user = req.user;
        const zohoAccountId = user.apiKeys.zoho.accountId;

        if (!zohoAccountId) {
            return res.status(400).json({
                success: false,
                message: 'Zoho account not configured'
            });
        }

        const folders = await zohoMailService.getFolders(user._id, zohoAccountId);

        res.json({
            success: true,
            data: folders
        });

    } catch (error) {
        console.error('Get folders error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get folders'
        });
    }
};

module.exports = {
    showDashboard,
    showInbox,
    showCompose,
    showEmailView,
    getDashboardData,
    markEmailStatus,
    searchEmails,
    getFolders
};

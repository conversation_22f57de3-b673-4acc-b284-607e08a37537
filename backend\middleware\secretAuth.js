const crypto = require('crypto');

// Generate a secure session token for verified users
const generateSecureToken = () => {
    return crypto.randomBytes(32).toString('hex');
};

// Middleware to check if user has entered the correct secret code
const requireSecretAuth = (req, res, next) => {
    // Check if user has already been verified in this session
    if (req.session.secretAuthVerified) {
        return next();
    }
    
    // If not verified, redirect to secret auth page
    return res.redirect('/auth/secret');
};

// Show secret auth code entry page
const showSecretAuth = (req, res) => {
    // If already verified, redirect to login
    if (req.session.secretAuthVerified) {
        return res.redirect('/auth/login');
    }

    const error = req.query.error || null;
    res.render('auth/secret-auth', {
        title: 'Access Code Required - Cythro Mail Panel',
        error,
        layout: false  // Disable layout for auth pages
    });
};

// Handle secret auth code verification
const verifySecretAuth = (req, res) => {
    try {
        const { secretCode } = req.body;
        const correctCode = process.env.SECRET_AUTH_CODE;
        
        // Security checks
        if (!secretCode || typeof secretCode !== 'string') {
            return res.redirect('/auth/secret?error=Invalid access code format');
        }
        
        if (!correctCode) {
            console.error('SECRET_AUTH_CODE not configured in environment');
            return res.status(500).render('errors/500', {
                title: 'Configuration Error',
                message: 'Server configuration error. Please contact administrator.'
            });
        }
        
        // Constant-time comparison to prevent timing attacks
        const providedHash = crypto.createHash('sha256').update(secretCode.trim()).digest('hex');
        const correctHash = crypto.createHash('sha256').update(correctCode).digest('hex');
        
        if (providedHash.length !== correctHash.length) {
            // Log failed attempt with IP
            console.warn(`Failed secret auth attempt from IP: ${req.ip || req.connection.remoteAddress} - Invalid code length`);
            return res.redirect('/auth/secret?error=Invalid access code');
        }
        
        // Use crypto.timingSafeEqual for constant-time comparison
        const providedBuffer = Buffer.from(providedHash, 'hex');
        const correctBuffer = Buffer.from(correctHash, 'hex');
        
        if (!crypto.timingSafeEqual(providedBuffer, correctBuffer)) {
            // Log failed attempt with IP and timestamp
            console.warn(`Failed secret auth attempt from IP: ${req.ip || req.connection.remoteAddress} at ${new Date().toISOString()}`);
            
            // Add a small delay to prevent brute force attacks
            setTimeout(() => {
                res.redirect('/auth/secret?error=Invalid access code');
            }, 1000 + Math.random() * 2000); // Random delay between 1-3 seconds
            return;
        }
        
        // Success - generate secure session token
        const sessionToken = generateSecureToken();
        req.session.secretAuthVerified = true;
        req.session.secretAuthToken = sessionToken;
        req.session.secretAuthTime = Date.now();
        
        // Set session to expire in 24 hours
        req.session.cookie.maxAge = 24 * 60 * 60 * 1000;
        
        // Log successful verification (without revealing the code)
        console.log(`Successful secret auth from IP: ${req.ip || req.connection.remoteAddress} at ${new Date().toISOString()}`);
        
        // Redirect to login page
        res.redirect('/auth/login');
        
    } catch (error) {
        console.error('Secret auth verification error:', error);
        res.redirect('/auth/secret?error=Authentication failed. Please try again.');
    }
};

// Middleware to check if secret auth session is still valid
const validateSecretAuthSession = (req, res, next) => {
    if (!req.session.secretAuthVerified || !req.session.secretAuthToken || !req.session.secretAuthTime) {
        req.session.secretAuthVerified = false;
        return res.redirect('/auth/secret');
    }
    
    // Check if session has expired (24 hours)
    const sessionAge = Date.now() - req.session.secretAuthTime;
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    if (sessionAge > maxAge) {
        req.session.secretAuthVerified = false;
        delete req.session.secretAuthToken;
        delete req.session.secretAuthTime;
        return res.redirect('/auth/secret?error=Session expired. Please enter the access code again.');
    }
    
    next();
};

// Clear secret auth session (for logout)
const clearSecretAuth = (req, res, next) => {
    req.session.secretAuthVerified = false;
    delete req.session.secretAuthToken;
    delete req.session.secretAuthTime;
    next();
};

module.exports = {
    requireSecretAuth,
    showSecretAuth,
    verifySecretAuth,
    validateSecretAuthSession,
    clearSecretAuth
};
